# Nuxt Starter Pro
$env:DEBUG='*'; pnpm dev - 最详细的终端信息输出
"dev": "DEBUG=vite:transform,tailwindcss nuxi dev"
或者 nuxt.config.ts 中 debug: true,
hmr 的慢主要是 tailwindcss 慢，可是为什么修改页面字符也需要重新渲染 tailwindcss 呢？

## Features

- [x] Nuxt 3
- [x] Tailwind CSS v4
- [x] Shadcn Vue 2.0.1

pnpm dlx shadcn-vue@2.0.1 add accordion alert alert-dialog aspect-ratio avatar badge breadcrumb button calendar card
pnpm dlx shadcn-vue@2.0.1 add checkbox collapsible command context-menu
pnpm dlx shadcn-vue@2.0.1 add dialog drawer dropdown-menu form hover-card input label menubar navigation-menu
pnpm dlx shadcn-vue@2.0.1 add pagination pin-input popover progress radio-group range-calendar resizable scroll-area select separator sheet sidebar skeleton slider sonner switch table tabs tags-input textarea toggle toggle-group tooltip
pnpm dlx shadcn-vue@2.0.1 add chart chart-area chart-bar chart-donut chart-line 

从 nuxt-auth-utils 迁移到 better-auth，以便使用基于 better-auth 的用户系统的最佳实践
auth.ts (auth 配置，客户端和服务端都会调用)
app/utils/auth-client.ts
server/utils/auth-server.ts
prisma/schema.prisma
server/api/auth/[...all].ts
.env
// better-auth 的设计, useUserSession 获取的 user 只会返回 better-auth 的指定字段, 其它字段通过自己的 user api 去获取
// getSession() 拿到的不是 实时查库 的 user，而是 登录时塞进 session 的一份快照。因此直接改 user 表，但 session 里保存的 JSON 还是旧的。需通过 authClient.updateUser 更新或 getSession 设置 disableCookieCache: true

