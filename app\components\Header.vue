<script setup lang="ts">
import { useWindowScroll } from '@vueuse/core'

interface NavItem {
  title: string
  href: string
}

withDefaults(defineProps<{
  mobileSecondaryNav?: NavItem[]
}>(), {
  mobileSecondaryNav: () => [],
})

const { y } = useWindowScroll()
// Fix hydration node mismatch
const clientEnv = ref(false)
onMounted(() => {
  clientEnv.value = true
})

const { brand } = useRuntimeConfig().public

const mobileMainNav = [
  // {
  //   title: 'Pricing',
  //   href: '/pricing',
  // },
  // {
  //   title: 'Blog',
  //   href: '/blog',  // 修正路径为 /blog
  // },
  // {
  //   title: 'Docs',
  //   href: '/docs',
  // },
]
</script>

<template>
  <header
    class="sticky top-0 z-40"
    :class="{'border-b bg-background/75 backdrop-blur-md': clientEnv && y > 50 }"
  >
    <div class="mx-auto w-full max-w-5xl px-6 md:max-w-7xl">
      <!-- mobile nav -->
      <div class="z-20 flex w-full flex-col items-center md:hidden">
        <div class="flex w-full items-center py-4">
          <div class="flex-auto">
            <NuxtLink
              class="text-xl font-semibold tracking-tight"
              href="/"
            >
              {{ brand }}
            </NuxtLink>
          </div>
          <div class="flex flex-auto justify-end">
            <MobileNav
              :nav="mobileMainNav"
              :secondary-nav="mobileSecondaryNav"
            />
          </div>
        </div>
      </div>

      <div class="mx-auto hidden h-[58px] items-center justify-between md:flex">
        <div class="flex-1">
          <NuxtLink
            class="py-1 text-xl font-semibold tracking-tight"
            :aria-label="brand"
            href="/"
          >
            {{ brand }}
          </NuxtLink>
        </div>
        <!-- nav -->
        <!-- <ul class="flex items-center">
          <li>
            <NuxtLink
              class="rounded-md px-2 py-1 text-sm font-semibold text-muted-foreground hover:text-foreground lg:px-4"
              href="/pricing"
            >
              Pricing
            </NuxtLink>
          </li>
          <li>
            <a
              class="rounded-md px-2 py-1 text-sm font-semibold text-muted-foreground hover:text-foreground lg:px-4"
              href="/blog"
            >
              Blog
            </a>
          </li>
          <li>
            <a
              class="rounded-md px-2 py-1 text-sm font-semibold text-muted-foreground hover:text-foreground lg:px-4"
              href="/docs"
            >
              Docs
            </a>
          </li>
        </ul> -->
        <div class="flex flex-1 justify-end gap-4">
          <ColorModeToggle />
          <ColorModeDropdown />
          <UserMenu />
        </div>
      </div>
    </div>
  </header>
</template>
