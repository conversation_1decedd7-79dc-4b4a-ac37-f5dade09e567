<script setup lang="ts">
import { authClient } from '@/utils/auth-client'

const loading = ref(true)
const tokenVerified = ref(false)

const route = useRoute()
const { token } = route.query
const email = ref()

const { fetch: refreshUserSession } = useUserSession()

onMounted(async () => {
  if (!token) {
    loading.value = false
    return
  }

  try {
    const { error } = await authClient.verifyEmail({
      query: {
        token: token as string
      }
    })

    if (error) {
      throw new Error(error.message || 'Email verification failed')
    }

    tokenVerified.value = true
    await refreshUserSession()
    toast('Email verified successfully')
    return navigateTo(`/dashboard`, {
      replace: true,
    })
  }
  catch (error) {
    console.error('Token verification failed:', error)
    tokenVerified.value = false
    toast.error(error.message || 'Email verification failed')
  }
  finally {
    loading.value = false
  }
})

const resendVerificationEmail = async () => {
  if (!email.value) {
    return
  }

  try {
    const { error } = await authClient.sendVerificationEmail({
      email: email.value
    })

    if (error) {
      throw new Error(error.message || 'Failed to send verification email')
    }

    toast.success('Verification email sent', {
      description: 'Please check your inbox for the verification link.'
    })
  }
  catch (error) {
    console.error('Failed to resend verification email:', error)
    toast.error(error.message || 'Failed to resend the verification email. Please try again later.')
  }
}
</script>

<template>
  <div class="flex items-center justify-center py-8">
    <div v-if="loading">
      Checking email verification status...
    </div>

    <div v-if="!loading && !tokenVerified">
      <p>The verification link is incorrect or has expired. Please request a new one.</p>
      <input
        v-model="email"
        type="email"
        placeholder="Enter your email"
      >
      <button @click="resendVerificationEmail">
        Resend Verification Email
      </button>
    </div>

    <div v-if="tokenVerified">
      <p>Email verification successful!</p>
    </div>
  </div>
</template>
