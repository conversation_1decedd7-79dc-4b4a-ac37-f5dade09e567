<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { authClient } from '@/utils/auth-client'

definePageMeta({
  title: 'Change Password',
  layout: false,
})

const loading = ref(false)

const formSchema = toTypedSchema(
  z.object({
    email: z.string().email(),
  }),
)

const form = useForm({
  validationSchema: formSchema,
})

const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    const { error } = await authClient.forgetPassword({
      email: values.email,
      redirectTo: '/auth/change-password'
    })

    if (error) {
      throw new Error(error.message || 'Failed to send reset email')
    }

    loading.value = false
    toast.success('Check your email', {
      description: 'We sent you instructions to reset your password.',
    })
  }
  catch (error) {
    loading.value = false
    toast.error(error.message || 'An unknown error occurred')
  }
})
</script>

<template>
  <Button
    as-child
    variant="ghost"
    class="absolute left-0 top-6 rounded-full text-sm font-semibold text-muted-foreground md:left-6"
  >
    <NuxtLink href="/">
      <Icon
        name="radix-icons:caret-left"
        class="mr-1"
      /> Home
    </NuxtLink>
  </Button>
  <div class="flex h-screen items-center justify-center">
    <main class="mx-auto min-h-[490px] w-full max-w-[450px]">
      <h1 class="mb-1.5 mt-8 text-center text-xl font-bold tracking-[-0.16px] sm:text-left">
        Reset password
      </h1>
      <p class="mb-8 text-center text-base font-normal text-muted-foreground sm:text-left">
        Include the email address associated with your account and we’ll send you an email with instructions to reset your password.
      </p>
      <form
        class="flex flex-col items-stretch gap-4"
        @submit.prevent="onSubmit"
      >
        <FormField
          v-slot="{ componentField }"
          name="email"
        >
          <FormItem>
            <FormLabel class="text-muted-foreground">
              Email
            </FormLabel>
            <FormControl>
              <Input
                v-bind="componentField"
                autocomplete="email"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
        <Button
          class="mt-2 w-full disabled:opacity-80"
          type="submit"
          size="lg"
          :disabled="!form.meta.value.valid"
        >
          <Icon
            v-show="loading"
            name="svg-spinners:3-dots-fade"
            class="mr-2 size-5"
          />
          <span>Send reset email</span>
        </Button>
      </form>
    </main>
  </div>
</template>
