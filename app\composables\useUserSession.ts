import { authClient } from '@/utils/auth-client'

// 基于 better-auth 的方法，兼容了 nuxt-auth-utils 的 useUserSession，避免修改太多代码
export function useUserSession() {
  const session = authClient.useSession()

  // 计算登录状态
  const loggedIn = computed(() => !!session.value?.data?.user)

  // 用户信息
  const currentUser = computed(() => session.value?.data?.user || null)

  // 清除会话（登出）
  const clearUserSession = async () => {
    await authClient.signOut()
  }

  // 刷新会话数据
  // 注意 getSession() 拿到的不是 实时查库 的 user，而是登录时塞进 session 的一份快照
  // 如果需要实时的 user 数据, disableCookieCache: true ,或者使用 authClient.updateUser 去修改
  const refreshUserSession = async () => {
    // await authClient.getSession({
    //   query: { disableCookieCache: true },
    //   fetchOptions: {
    //     cache: 'no-cache' // 确保不使用缓存
    //   }
    // })
    // getSession 在扩展端不能更新 session ，改为使用 refetch
    await (session.value as any).refetch();
  }

  return {
    // 新的结构
    user: readonly(currentUser),
    clear: clearUserSession,
    fetch: refreshUserSession,

    // 保持向后兼容的属性
    loggedIn: readonly(loggedIn),

    // 原始会话对象
    session
  }
}