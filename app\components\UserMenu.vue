<script setup lang="ts">
// 延迟获取用户信息，避免水合问题
const currentUser = ref(null)
const { user: currentUserFromSession, clear: clearUserSession, fetch: refreshUserSession } = useUserSession()

onMounted(() => {
  currentUser.value = currentUserFromSession.value

  // 监听用户状态变化
  watch(currentUserFromSession, (newValue) => {
    currentUser.value = newValue
  })
})

async function logOut() {
  await clearUserSession()
  navigateTo('/')
}
</script>

<template>
    <div v-if="currentUser">
      <Button @click="refreshUserSession()">
        测试刷新
      </Button>
      <DropdownMenu>
        <DropdownMenuTrigger class="text-muted-foreground hover:text-foreground">
          {{ currentUser?.email }} <Icon name="lucide:chevrons-up-down" />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          class="w-56"
          align="end"
        >
          <DropdownMenuLabel class="flex font-normal">
            <div class="grid flex-1 text-left text-sm leading-tight">
              <span class="truncate font-semibold">{{ currentUser?.name }}</span>
              <span class="truncate text-xs text-muted-foreground">{{ currentUser?.email }}</span>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem as-child>
            <NuxtLink href="/">
              <Icon
                name="lucide:slash"
                class="mr-2"
              /> Home
            </NuxtLink>
          </DropdownMenuItem>
          <DropdownMenuItem as-child>
            <NuxtLink href="/dashboard">
              <Icon
                name="lucide:layout-dashboard"
                class="mr-2"
              /> Dashboard
            </NuxtLink>
          </DropdownMenuItem>
          <DropdownMenuItem
            as-child
            :disabled="currentUser?.role !== 'ADMIN'"
          >
            <NuxtLink href="/admin">
              <Icon
                name="lucide:chart-scatter"
                class="mr-2"
              /> Admin
            </NuxtLink>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem as-child>
              <NuxtLink href="/pricing">
                <Icon
                  name="lucide:sparkles"
                  class="mr-2"
                /> Upgrade to Pro
              </NuxtLink>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            <DropdownMenuItem as-child>
              <NuxtLink href="/dashboard/settings">
                Account
              </NuxtLink>
            </DropdownMenuItem>
            <DropdownMenuItem as-child>
              <NuxtLink href="/dashboard/settings/billing">
                Billing
              </NuxtLink>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem @click="logOut">
            <Icon
              name="lucide:log-out"
              class="mr-2"
            />
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
    <div v-else>
      <Button
        as-child
        variant="ghost"
        class="rounded-full"
        size="lg"
      >
        <NuxtLink
          class="font-semibold text-muted-foreground hover:text-foreground"
          href="/auth/login"
        >
          Sign in
        </NuxtLink>
      </Button>
      <Button
        as-child
        class="rounded-full"
        size="lg"
      >
        <NuxtLink href="/auth/signup">
          Get Started <Icon
            name="radix-icons:caret-right"
            class="-mr-1"
          /></NuxtLink>
      </Button>
    </div>
</template>
