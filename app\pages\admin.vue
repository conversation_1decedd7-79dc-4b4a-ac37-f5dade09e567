<script setup lang="ts">
definePageMeta({
  title: 'Admin Layout',
  layout: false,
  middleware: ['auth', 'admin'],
})

const { brand } = useRuntimeConfig().public

const { user: currentUser, clear: clearUserSession } = useUserSession()

const logOut = async () => {
  await clearUserSession()
  return navigateTo('/')
}

const data = {
  projects: [
    {
      name: 'Users',
      url: '/admin/users',
      icon: 'lucide:frame',
    },
    {
      name: 'Subscriptions',
      url: '/admin/subscriptions',
      icon: 'lucide:pie-chart',
    },
    {
      name: 'Images',
      url: '/admin/images',
      icon: 'lucide:pie-chart',
    },
    {
      name: 'Bookmarks',
      url: '/admin/bookmarks',
      icon: 'lucide:map',
    },
  ],
  navMain: [
    {
      title: 'Playground',
      url: '#',
      icon: 'lucide:square-terminal',
      isActive: true,
      items: [
        {
          title: 'History',
          url: '#',
        },
        {
          title: 'Starred',
          url: '#',
        },
        {
          title: 'Settings',
          url: '#',
        },
      ],
    },
    {
      title: 'Models',
      url: '#',
      icon: 'lucide:bot',
      items: [
        {
          title: 'Genesis',
          url: '#',
        },
        {
          title: 'Explorer',
          url: '#',
        },
        {
          title: 'Quantum',
          url: '#',
        },
      ],
    },
    {
      title: 'Documentation',
      url: '#',
      icon: 'lucide:book-open',
      items: [
        {
          title: 'Introduction',
          url: '#',
        },
        {
          title: 'Get Started',
          url: '#',
        },
        {
          title: 'Tutorials',
          url: '#',
        },
        {
          title: 'Changelog',
          url: '#',
        },
      ],
    },
    {
      title: 'Settings',
      url: '#',
      icon: 'lucide:settings-2',
      items: [
        {
          title: 'General',
          url: '#',
        },
        {
          title: 'Team',
          url: '#',
        },
        {
          title: 'Billing',
          url: '#',
        },
        {
          title: 'Limits',
          url: '#',
        },
      ],
    },
  ],
}
</script>

<template>
  <div id="dashboard-layout">
    <SidebarProvider>
      <Sidebar
        collapsible="icon"
        class="bg-background"
      >
        <SidebarHeader class="py-4">
          <div class="flex items-center px-2 ">
            <NuxtLink
              class="text-lg font-semibold tracking-tight group-data-[collapsible=icon]:hidden"
              href="/admin"
            >
              {{ brand }} <span class="text-xs font-normal text-muted-foreground">/ admin</span>
            </NuxtLink>
            <NuxtLink
              class="hidden text-lg font-semibold tracking-tight group-data-[collapsible=icon]:block"
              href="/admin"
            >
              {{ brand[0] }}
            </NuxtLink>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup class="group-data-[collapsible=icon]:hidden">
            <SidebarGroupLabel class="text-muted-foreground">
              Projects
            </SidebarGroupLabel>
            <SidebarMenu>
              <SidebarMenuItem
                v-for="item in data.projects"
                :key="item.name"
              >
                <SidebarMenuButton as-child>
                  <NuxtLink :href="item.url">
                    <Icon
                      :name="item.icon"
                      class="text-muted-foreground"
                    />
                    <span>{{ item.name }}</span>
                  </NuxtLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>
          <SidebarGroup>
            <SidebarGroupLabel class="text-muted-foreground">
              Example
            </SidebarGroupLabel>
            <SidebarMenu>
              <Collapsible
                v-for="item in data.navMain"
                :key="item.title"
                as-child
                :default-open="item.isActive"
                class="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger as-child>
                    <SidebarMenuButton
                      :tooltip="item.title"
                      class="group/menu"
                    >
                      <Icon
                        :name="item.icon"
                        class="text-muted-foreground"
                      />
                      <span>{{ item.title }}</span>
                      <Icon
                        name="lucide:chevron-right"
                        class="ml-auto transition-transform duration-200 group-data-[state=open]/menu:rotate-90"
                      />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      <SidebarMenuSubItem
                        v-for="subItem in item.items"
                        :key="subItem.title"
                      >
                        <SidebarMenuSubButton as-child>
                          <a :href="subItem.url">
                            <span>{{ subItem.title }}</span>
                          </a>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
              <SidebarMenuItem>
                <SidebarMenuButton class="text-sidebar-foreground/70">
                  <Icon
                    name="lucide:more-horizontal"
                    class="text-sidebar-foreground/70"
                  />
                  <span>More</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>
      </Sidebar>
      <SidebarInset>
        <header class="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-16">
          <div class="flex items-center gap-2 px-4">
            <SidebarTrigger class="-ml-1" />
            <Separator
              orientation="vertical"
              class="mr-2 h-4"
            />
            <AdminBreadcrumbs />
          </div>
          <DropdownMenu v-if="currentUser">
            <DropdownMenuTrigger as-child>
              <Button
                variant="ghost"
                class="mr-4 size-8 rounded-full"
              >
                <Avatar class="size-7">
                  <AvatarImage
                    :src="currentUser.image"
                    :alt="currentUser.name"
                  />
                  <AvatarFallback>{{ avatarName(currentUser.name) }}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              class="w-56"
              align="end"
            >
              <DropdownMenuLabel class="flex font-normal">
                <div class="grid flex-1 text-left text-sm leading-tight">
                  <span class="truncate font-semibold">{{ currentUser.name }}</span>
                  <span class="truncate text-xs text-muted-foreground">{{ currentUser.email }}</span>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem as-child>
                  <NuxtLink href="/">
                    Home
                  </NuxtLink>
                </DropdownMenuItem>
                <DropdownMenuItem as-child>
                  <NuxtLink href="/dashboard">
                    Dashboard
                  </NuxtLink>
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem as-child>
                  <NuxtLink href="/dashboard/settings">
                    Settings
                  </NuxtLink>
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem @click="logOut">
                <Icon
                  name="lucide:log-out"
                  class="mr-2"
                />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </header>
        <div class="flex flex-col gap-4 p-4 pt-0">
          <NuxtPage />
        </div>
      </SidebarInset>
    </SidebarProvider>
  </div>
</template>
