<script setup lang="ts">
import type { Environments, Paddle } from '@paddle/paddle-js'
import { initializePaddle } from '@paddle/paddle-js'

definePageMeta({
  title: 'Paddle Checkout',
  layout: false,
  middleware: ['auth'],
})

interface PathParams {
  priceId: string
}

const route = useRoute()
const priceId = Array.isArray(route.params.priceId)
  ? route.params.priceId[0]
  : route.params.priceId
const runtimeConfig = useRuntimeConfig()
const { user: currentUser, clear: clearUserSession } = useUserSession()

const quantity = ref(1)
const paddle = ref<Paddle | null>(null)

const paddleClientToken = import.meta.env.VITE_PADDLE_CLIENT_TOKEN
const paddleEnv = import.meta.env.VITE_PADDLE_ENV

const checkoutData = ref(null)

const handleCheckoutEvents = (event) => {
  checkoutData.value = event.data
}

onMounted(() => {
  if (!paddle.value && paddleClientToken && paddleEnv) {
    initializePaddle({
      token: paddleClientToken,
      environment: paddleEnv as Environments,
      eventCallback: (event) => {
        if (event.data && event.name) {
          handleCheckoutEvents(event)
        }
      },
      checkout: {
        settings: {
          displayMode: 'inline',
          theme: 'light',
          // variant: 'one-page',
          allowLogout: !currentUser.value, // allow change email
          frameTarget: 'paddle-checkout-frame',
          frameInitialHeight: 450,
          frameStyle: 'width: 100%; background-color: transparent; border: none',
          successUrl: '/dashboard/settings/billing',
        },
      },
    }).then((p) => {
      if (p && priceId) {
        paddle.value = p
        paddle.value.Checkout.open({
          ...(currentUser.value && { customer: { email: currentUser.value.email } }),
          customData: {
            user_id: currentUser.value?.id,
          },
          items: [{ priceId: priceId, quantity: 1 }],
        })
      }
    })
  }
})

watch([paddle, quantity], () => {
  if (paddle.value && priceId && paddle.value.Initialized) {
    paddle.value.Checkout.updateItems([{ priceId: priceId, quantity: quantity.value }])
  }
}, { immediate: true })

function LoadingText({ value, currencyCode }) {
  if (value === undefined) {
    return null
  }
  else {
    return formatMoney(value, currencyCode)
  }
}
</script>

<template>
  <div class="relative min-h-screen flex-col justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
    <section class="h-full flex-col items-end bg-muted p-10 md:pt-20 lg:flex dark:border-r dark:bg-background">
      <div class="md:w-[400px]">
        <div
          v-if="checkoutData"
          class="md:block"
        >
          <div class="text-base font-semibold leading-[20px]">
            {{ checkoutData?.items[0].product.name }}
          </div>
          <div class="flex items-end gap-2 pt-8">
            <span class="text-5xl"><LoadingText
              :currency-code="checkoutData?.currency_code"
              :value="checkoutData?.totals.total"
            /></span>
            <span class="text-base leading-[16px]">inc. tax</span>
          </div>
          <div class="pt-4 text-base font-medium leading-[20px] text-muted-foreground">
            then
            <LoadingText
              :currency-code="checkoutData?.currency_code"
              :value="checkoutData?.totals.subtotal"
            />
            / {{ checkoutData?.items[0].billing_cycle?.frequency }} {{ checkoutData?.items[0].billing_cycle?.interval }}
          </div>
          <div
            data-orientation="horizontal"
            role="none"
            class="mt-6 h-px w-full shrink-0 bg-border"
          />
          <div class="mt-6 flex justify-between pt-6">
            <span class="text-base font-medium leading-[20px] text-muted-foreground">Subtotal</span>
            <span class="text-base font-semibold leading-[20px]">
              <LoadingText
                :currency-code="checkoutData?.currency_code"
                :value="checkoutData?.totals.subtotal"
              />
            </span>
          </div>
          <div class="flex justify-between pt-6">
            <span class="text-base font-medium leading-[20px] text-muted-foreground">Tax</span>
            <span class="text-base font-semibold leading-[20px]">
              <LoadingText
                :currency-code="checkoutData?.currency_code"
                :value="checkoutData?.totals.tax"
              />
            </span>
          </div>
          <div
            data-orientation="horizontal"
            role="none"
            class="mt-6 h-px w-full shrink-0 bg-border"
          />
          <div class="flex justify-between pt-6">
            <span class="text-base font-medium leading-[20px] text-muted-foreground">Due today</span>
            <span class="text-base font-semibold leading-[20px]">
              <LoadingText
                :currency-code="checkoutData?.currency_code"
                :value="checkoutData?.totals.total"
              />
            </span>
          </div>
        </div>
      </div>
    </section>
    <section class="bg-white py-10 md:pt-20">
      <div class="mx-8 flex flex-col space-y-6 md:w-[400px]">
        <div
          id="paddle-checkout-frame"
          class="paddle-checkout-frame"
        />
      </div>
    </section>
  </div>
</template>
