import { z } from 'zod'
import { nanoid } from 'nanoid'

export default defineEventHandler(async (event) => {
  const { user } = await requireUserSession(event)

  const sharedCount = await db.shared.count({
    where: {
      userId: user.id,
    },
  })

  if (user.plan === 'free' && sharedCount > 0) {
    throw createError({
      statusCode: 403,
      statusMessage: 'Free plan users can only create one shared.',
    })
  }

  const { source, browserAccountId, bookmarkId, bookmarkData, bookmarkDataHash, description, isPublic } = await readValidatedBody(event, body =>
    z.object({
      source: z.string(),
      browserAccountId: z.string().optional(),
      bookmarkId: z.string().optional(),
      bookmarkData: z.string(),
      bookmarkDataHash: z.string(),
      description: z.string().optional(),
      isPublic: z.boolean().optional(),
    }).parse(body),
  )

  const shared = await db.shared.create({
    data: {
      slug: nanoid(10),
      description,
      isPublic,
      userId: user.id,
      source,
      browserAccountId,
      bookmarkId,
      bookmarkData,
      bookmarkDataHash,
      updatedAt: new Date(),
      createdAt: new Date(),
    },
  })

  return shared
})
