<script setup lang="ts">
// 基于 @nuxtjs/color-mode 的简单实现，自动持久化，SSR，无闪烁
const colorMode = useColorMode()

// 切换明暗模式
const toggleTheme = () => {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}

// 计算当前是否为深色模式
const isDark = computed(() => colorMode.value === 'dark')
</script>

<template>
  <ClientOnly>
    <Button
      variant="ghost"
      size="icon"
      @click="toggleTheme"
      aria-label="Toggle color mode"
    >
      <Icon
        v-if="isDark"
        name="lucide:moon"
        class="h-4 w-4"
      />
      <Icon
        v-else
        name="lucide:sun"
        class="h-4 w-4"
      />
    </Button>
  </ClientOnly>
</template>
