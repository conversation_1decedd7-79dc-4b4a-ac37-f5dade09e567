<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { authClient } from '@/utils/auth-client'

const loading = ref(false)

const formSchema = toTypedSchema(
  z.object({
    name: z.string().min(4).max(255).optional().or(z.literal('')),
    email: z.string().email(),
    password: z.string().min(6, 'Must be at least 6 characters'),
  }),
)

const form = useForm({
  validationSchema: formSchema,
})

const onSubmit = form.handleSubmit(async (values) => {
  try {
    loading.value = true
    const { error } = await authClient.signUp.email({
      email: values.email,
      password: values.password,
      name: values.name || undefined,
      callbackURL: '/dashboard'
    })

    if (error) {
      throw new Error(error.message || 'Signup failed')
    }

    toast({
      title: 'Account created',
    })
    return navigateTo('/dashboard')
  } catch (error) {
    toast.error(error.message || 'An unknown error occurred')
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <form
    class="flex flex-col items-stretch gap-4"
    @submit.prevent="onSubmit"
  >
    <FormField
      v-slot="{ componentField }"
      name="name"
    >
      <FormItem>
        <FormLabel class="text-muted-foreground">
          Name
        </FormLabel>
        <FormControl>
          <Input
            type="text"
            placeholder=""
            v-bind="componentField"
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    </FormField>

    <FormField
      v-slot="{ componentField }"
      name="email"
    >
      <FormItem>
        <FormLabel class="text-muted-foreground">
          Email
        </FormLabel>
        <FormControl>
          <Input
            v-bind="componentField"
            autocomplete="email"
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    </FormField>

    <FormField
      v-slot="{ componentField }"
      name="password"
    >
      <FormItem>
        <FormLabel class="text-muted-foreground">
          Password
        </FormLabel>
        <FormControl>
          <Input
            type="password"
            v-bind="componentField"
          />
        </FormControl>
        <FormMessage />
      </FormItem>
    </FormField>

    <Button
      class="mt-2 w-full cursor-pointer disabled:opacity-60"
      type="submit"
      size="lg"
      :disabled="!form.meta.value.valid"
    >
      Create account
    </Button>
  </form>
</template>
