<script setup lang="ts">
const props = withDefaults(defineProps<{
  nav: any[]
  secondaryNav?: any[]
}>(), {
  nav: () => [],
  secondaryNav: () => [],
})

const open = ref(false)
</script>

<template>
  <Sheet v-model:open="open">
    <SheetTrigger as-child>
      <Icon
        name="radix-icons:hamburger-menu"
        class="size-6"
      />
    </SheetTrigger>
    <SheetContent
      side="left"
      class="pr-0"
    >
      <SheetTitle />
      <SheetDescription />
      <ScrollArea class="mt-[20px] h-[calc(100vh-45px)] pb-[40px]">
        <!-- 明暗模式切换 -->
        <div class="mb-6 flex items-center justify-between">
          <span class="text-sm font-medium">主题模式</span>
          <ColorModeToggle />
        </div>

        <div class="flex flex-col space-y-3">
          <template
            v-for="item in props.nav"
            :key="item.href"
          >
            <!-- 对于 /docs 和 /blog 路径使用 a 标签，因为它们是 VitePress 生成的静态文件 -->
            <a
              v-if="item.href === '/docs' || item.href === '/blog'"
              :href="item.href"
              @click="open = false"
            >
              {{ item.title }}
            </a>
            <!-- 其他路径继续使用 NuxtLink -->
            <NuxtLink
              v-else
              :href="item.href"
              @click="open = false"
            >
              {{ item.title }}
            </NuxtLink>
          </template>
        </div>
        <div class="flex flex-col space-y-2">
          <div
            v-for="(items, index) in props.secondaryNav"
            :key="index"
            class="flex flex-col space-y-3 pt-6"
          >
            <div class="flex items-center">
              <h4 class="font-medium">
                {{ items.title }}
              </h4>
              <span
                v-if="items.label"
                class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs leading-none text-[#000000] no-underline group-hover:no-underline"
              >
                {{ items.label }}
              </span>
            </div>

            <template
              v-for="item in items.items"
              :key="item.href"
            >
              <!-- 对于 /docs 和 /blog 路径使用 a 标签，因为它们是 VitePress 生成的静态文件 -->
              <a
                v-if="item.href === '/docs' || item.href === '/blog'"
                :href="item.href"
                class="inline-flex items-center text-muted-foreground"
                @click="open = false"
              >
                {{ item.title }}
                <span
                  v-if="item.label"
                  class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs leading-none text-[#000000] no-underline group-hover:no-underline"
                >
                  {{ item.label }}
                </span>
              </a>
              <!-- 其他路径继续使用 NuxtLink -->
              <NuxtLink
                v-else
                :href="item.href"
                class="inline-flex items-center text-muted-foreground"
                @click="open = false"
              >
                {{ item.title }}
                <span
                  v-if="item.label"
                  class="ml-2 rounded-md bg-[#adfa1d] px-1.5 py-0.5 text-xs leading-none text-[#000000] no-underline group-hover:no-underline"
                >
                  {{ item.label }}
                </span>
              </NuxtLink>
            </template>
          </div>
        </div>
      </ScrollArea>
    </SheetContent>
  </Sheet>
</template>
