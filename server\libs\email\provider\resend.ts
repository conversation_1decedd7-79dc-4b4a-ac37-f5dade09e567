import type { Send<PERSON><PERSON>Handler } from '../types'

export const sendEmail: SendEmailHandler = async ({ to, subject, html, text, from }) => {
  if (!process.env.RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY is missing')
  }

  // 修改验证逻辑：html 和 text 至少要有一个
  if (!to || !from || !subject || (!html && !text)) {
    throw new Error('Required email fields are missing')
  }

  try {
    const emailBody: any = {
      to,
      from,
      subject,
    }

    // 优先使用 html，如果没有则使用 text
    if (html) {
      emailBody.html = html
    } else if (text) {
      emailBody.text = text
    }

    await $fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
      },
      body: emailBody,
    })

    console.log('Email sent successfully via Resend')
  }
  catch (error) {
    console.error('Failed to send email with Resend: ', error)
    throw error
  }
}
